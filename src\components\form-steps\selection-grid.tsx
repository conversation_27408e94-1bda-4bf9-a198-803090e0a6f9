import { <PERSON><PERSON>, Card, CardContent, FormControlLabel, Checkbox, Stack, Typography, Box } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { ReactNode } from 'react';

// ----------------------------------------------------------------------

interface SelectionItem {
  id: string | number;
  label: string;
  description?: string;
  icon?: string;
  disabled?: boolean;
}

interface SelectionGridProps {
  items: SelectionItem[];
  selectedItems: (string | number)[];
  onToggle: (id: string | number) => void;
  multiSelect?: boolean;
  columns?: { xs?: number; sm?: number; md?: number; lg?: number };
  renderCustomContent?: (item: SelectionItem) => ReactNode;
}

export function SelectionGrid({
  items,
  selectedItems,
  onToggle,
  multiSelect = true,
  columns = { xs: 1, sm: 2, md: 2, lg: 3 },
  renderCustomContent,
}: SelectionGridProps) {
  return (
    <Grid container spacing={2}>
      {items.map((item) => {
        const isSelected = selectedItems.includes(item.id);
        
        return (
          <Grid item {...columns} key={item.id}>
            <Card
              variant="outlined"
              sx={{
                cursor: item.disabled ? 'not-allowed' : 'pointer',
                bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                borderColor: isSelected ? 'primary.main' : 'divider',
                borderWidth: isSelected ? 2 : 1,
                opacity: item.disabled ? 0.5 : 1,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  borderColor: item.disabled ? 'divider' : 'primary.main',
                  bgcolor: item.disabled ? 'background.paper' : isSelected ? 'primary.lighter' : 'action.hover',
                },
              }}
              onClick={() => !item.disabled && onToggle(item.id)}
            >
              <CardContent sx={{ p: 2 }}>
                {renderCustomContent ? (
                  renderCustomContent(item)
                ) : (
                  <FormControlLabel
                    control={
                      multiSelect ? (
                        <Checkbox checked={isSelected} disabled={item.disabled} />
                      ) : (
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            border: '2px solid',
                            borderColor: isSelected ? 'primary.main' : 'divider',
                            bgcolor: isSelected ? 'primary.main' : 'transparent',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {isSelected && (
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                bgcolor: 'common.white',
                              }}
                            />
                          )}
                        </Box>
                      )
                    }
                    label={
                      <Stack direction="row" alignItems="center" spacing={1.5} sx={{ width: '100%' }}>
                        {item.icon && <Iconify icon={item.icon} width={24} />}
                        <Stack spacing={0.5} sx={{ flex: 1 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {item.label}
                          </Typography>
                          {item.description && (
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                              {item.description}
                            </Typography>
                          )}
                        </Stack>
                      </Stack>
                    }
                    sx={{ 
                      width: '100%', 
                      m: 0,
                      '& .MuiFormControlLabel-label': {
                        width: '100%',
                      },
                    }}
                  />
                )}
              </CardContent>
            </Card>
          </Grid>
        );
      })}
    </Grid>
  );
}

export default SelectionGrid;
