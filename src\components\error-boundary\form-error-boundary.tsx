import React, { Component, ReactNode } from 'react';
import { Box, Typography, Button, Alert, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class FormErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Form Error Boundary caught an error:', error, errorInfo);
    const { onError } = this.props;
    onError?.(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    const { hasError, error } = this.state;
    const { fallback, children } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 400,
            p: 3,
          }}
        >
          <Alert 
            severity="error" 
            sx={{ mb: 3, maxWidth: 600 }}
            icon={<Iconify icon="eva:alert-triangle-fill" />}
          >
            <Typography variant="h6" sx={{ mb: 1 }}>
              Something went wrong
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }}>
              An error occurred while loading the form. Please try refreshing the page or contact support if the problem persists.
            </Typography>
            {process.env.NODE_ENV === 'development' && error && (
              <Box
                component="pre"
                sx={{
                  mt: 2,
                  p: 2,
                  bgcolor: 'grey.100',
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  overflow: 'auto',
                  maxHeight: 200,
                }}
              >
                {error.message}
                {'\n'}
                {error.stack}
              </Box>
            )}
          </Alert>

          <Stack direction="row" spacing={2}>
            <Button
              variant="contained"
              onClick={this.handleReset}
              startIcon={<Iconify icon="eva:refresh-fill" />}
            >
              Try Again
            </Button>
            <Button
              variant="outlined"
              onClick={() => window.location.reload()}
              startIcon={<Iconify icon="eva:refresh-outline" />}
            >
              Refresh Page
            </Button>
          </Stack>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default FormErrorBoundary;
