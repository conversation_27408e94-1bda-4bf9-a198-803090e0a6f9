import { Stack, Icon<PERSON>utton, Typography, Box, Container, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { Form } from 'src/components/hook-form/form-provider';
import { App<PERSON><PERSON>on, AppContainer } from 'src/components/common';
import { FormStepper } from 'src/components/form-steps';
import { FormErrorBoundary } from 'src/components/error-boundary';
import { paths } from 'src/routes/paths';
import { Template } from 'src/services/api/use-templates-api';
import { useAgentFormImproved } from './hooks/use-agent-form-improved';
import { DetailsStep, CategoryStep, ToolsStep, ModelStep } from './steps';

// ----------------------------------------------------------------------

interface AgentFormImprovedProps {
  agent: Template | null;
}

export function AgentFormImproved({ agent }: AgentFormImprovedProps) {
  const { t } = useTranslation();
  
  const {
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    handleNext,
    handleBack,
    handleStepClick,
    onFormSubmit,
    getCurrentStepErrors,
    isStepCompleted,
    canProceedToNext,
    steps,
    totalSteps,
    isLastStep,
    isFirstStep,
  } = useAgentFormImproved({ agent });
  
  // Step components mapping
  const stepComponents = [
    <DetailsStep key="details" />,
    <CategoryStep key="category" />,
    <ToolsStep key="tools" />,
    <ModelStep key="model" />,
  ];
  
  // Get current step errors for display
  const currentStepErrors = getCurrentStepErrors();
  
  return (
    <FormErrorBoundary>
      {/* Back Navigation */}
      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
        <IconButton
          onClick={() => window.history.back()}
          sx={{
            bgcolor: 'background.neutral',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Iconify icon="eva:arrow-left-fill" />
        </IconButton>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {t('components.common.back')}
        </Typography>
      </Stack>

      <AppContainer
        title={isEditing ? 'Edit Agent Template' : 'Create Agent Template'}
        pageTitle={isEditing ? 'Edit Agent Template' : 'Create Agent Template'}
        routeLinks={[
          { name: "Agent's Templates", href: paths.dashboard.agents.root },
          { name: isEditing ? 'Edit Template' : 'Create Template' },
        ]}
      >
        <Form methods={methods} onSubmit={onFormSubmit}>
          {/* Progress Stepper */}
          <FormStepper
            steps={steps}
            activeStep={activeStep}
            onStepClick={handleStepClick}
            isStepCompleted={isStepCompleted}
            isStepError={(stepIndex) => {
              const step = steps[stepIndex];
              return step.fields.some(field => methods.formState.errors[field]);
            }}
          />

          {/* Error Alert */}
          {currentStepErrors.length > 0 && (
            <Alert 
              severity="error" 
              sx={{ mb: 3 }}
              icon={<Iconify icon="eva:alert-circle-fill" />}
            >
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Please fix the following errors:
              </Typography>
              <Box component="ul" sx={{ m: 0, pl: 2 }}>
                {currentStepErrors.map((field) => (
                  <li key={field}>
                    <Typography variant="body2">
                      {methods.formState.errors[field]?.message}
                    </Typography>
                  </li>
                ))}
              </Box>
            </Alert>
          )}

          {/* Step Content */}
          <Container maxWidth="md" sx={{ px: 0 }}>
            <Box sx={{ minHeight: 400 }}>
              {stepComponents[activeStep]}
            </Box>
          </Container>

          {/* Navigation Buttons */}
          <Stack 
            direction="row" 
            justifyContent="space-between" 
            alignItems="center"
            sx={{ 
              mt: 4,
              pt: 3,
              borderTop: '1px solid',
              borderColor: 'divider',
            }}
          >
            {/* Back Button */}
            <AppButton
              label="Back"
              variant="outlined"
              onClick={handleBack}
              isShow={!isFirstStep}
              startIcon={<Iconify icon="eva:arrow-left-fill" />}
              sx={{ minWidth: 120 }}
            />

            <Box sx={{ flexGrow: 1 }} />

            {/* Step Progress */}
            <Typography 
              variant="body2" 
              sx={{ 
                color: 'text.secondary',
                mx: 2,
                display: { xs: 'none', sm: 'block' }
              }}
            >
              Step {activeStep + 1} of {totalSteps}
            </Typography>

            {/* Next/Submit Button */}
            {isLastStep ? (
              <AppButton
                label={isEditing ? 'Update Template' : 'Create Template'}
                variant="contained"
                onClick={onFormSubmit}
                isLoading={isLoading || isSubmitting}
                disabled={!canProceedToNext()}
                startIcon={<Iconify icon={isEditing ? "eva:save-fill" : "eva:plus-fill"} />}
                sx={{ minWidth: 160 }}
              />
            ) : (
              <AppButton
                label="Next"
                variant="contained"
                onClick={handleNext}
                disabled={!canProceedToNext()}
                endIcon={<Iconify icon="eva:arrow-right-fill" />}
                sx={{ minWidth: 120 }}
              />
            )}
          </Stack>
        </Form>
      </AppContainer>
    </FormErrorBoundary>
  );
}

export default AgentFormImproved;
