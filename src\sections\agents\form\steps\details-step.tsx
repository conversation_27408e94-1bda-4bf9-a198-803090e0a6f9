import { Stack, Typography, Box } from '@mui/material';
import { Field } from 'src/components/hook-form/fields';
import { FormStepWrapper, SelectionGrid } from 'src/components/form-steps';
import { AGENT_TYPE_OPTIONS, AGENT_STATUS_OPTIONS, FORM_FIELD_CONFIG } from '../config/agent-form-config';

// ----------------------------------------------------------------------

interface DetailsStepProps {
  // Add any specific props if needed
}

export function DetailsStep(_props: DetailsStepProps) {
  return (
    <FormStepWrapper
      title="Agent Details"
      description="Provide basic information about your agent template"
    >
      <Stack spacing={3}>
        {/* Name Field */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            {FORM_FIELD_CONFIG.name.label}
          </Typography>
          <Field.Text
            name="name"
            placeholder={FORM_FIELD_CONFIG.name.placeholder}
            helperText={FORM_FIELD_CONFIG.name.helperText}
          />
        </Box>

        {/* Description Field */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            {FORM_FIELD_CONFIG.description.label}
          </Typography>
          <Field.Text
            name="description"
            placeholder={FORM_FIELD_CONFIG.description.placeholder}
            helperText={FORM_FIELD_CONFIG.description.helperText}
            multiline
            rows={FORM_FIELD_CONFIG.description.rows}
          />
        </Box>

        {/* System Message Field */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            {FORM_FIELD_CONFIG.systemMessage.label}
          </Typography>
          <Field.Text
            name="systemMessage"
            placeholder={FORM_FIELD_CONFIG.systemMessage.placeholder}
            helperText={FORM_FIELD_CONFIG.systemMessage.helperText}
            multiline
            rows={FORM_FIELD_CONFIG.systemMessage.rows}
          />
        </Box>

        {/* Agent Type Selection */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
            Agent Type
          </Typography>
          <Field.RadioGroup
            name="type"
            options={AGENT_TYPE_OPTIONS.map(option => ({
              value: option.value,
              label: option.label,
            }))}
            sx={{
              '& .MuiFormControlLabel-root': {
                bgcolor: 'background.neutral',
                borderRadius: 1.5,
                p: 2,
                m: 0.5,
                border: '1px solid',
                borderColor: 'divider',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'primary.lighter',
                },
              },
              '& .MuiRadio-root.Mui-checked + .MuiFormControlLabel-label': {
                fontWeight: 600,
              },
            }}
          />
        </Box>

        {/* Agent Status */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
            Status
          </Typography>
          <Field.Switch
            name="status"
            options={AGENT_STATUS_OPTIONS.map(option => ({
              value: option.value,
              label: option.label,
            }))}
            labelPlacement="end"
            sx={{
              '& .MuiFormControlLabel-root': {
                bgcolor: 'background.neutral',
                borderRadius: 1.5,
                p: 2,
                m: 0,
                border: '1px solid',
                borderColor: 'divider',
              },
            }}
          />
        </Box>
      </Stack>
    </FormStepWrapper>
  );
}

export default DetailsStep;
