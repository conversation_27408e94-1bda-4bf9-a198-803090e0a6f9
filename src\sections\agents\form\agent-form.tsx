import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  Container,
  Button,
  Radio,
  RadioGroup,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton, AppContainer } from 'src/components/common';
import { t } from 'i18next';
import { paths } from 'src/routes/paths';
import { Template } from 'src/services/api/use-templates-api';
import { useState } from 'react';
import { useAgentForm, AgentFormValues } from './use-agent-form';
import ServiceSearchBar from './components/service-search-bar';

// ----------------------------------------------------------------------

// Component props
interface AgentFormProps {
  agent: Template | null;
  onSubmit: (data: AgentFormValues) => void;
}

export default function AgentForm({ agent, onSubmit }: AgentFormProps) {
  // Use the custom hook for form logic
  const {
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    availableTools,
    categories,
    TYPE_OPTIONS,
    STATUS_OPTIONS,
    isLoading,
    options,
    selectedChannels,
    handleChannelToggle,
    CHANNEL_OPTIONS,
    toolSearchQuery,
    categorySearchQuery,
    channelSearchQuery,
    handleToolSearchChange,
    handleCategorySearchChange,
    handleChannelSearchChange,
  } = useAgentForm({ agent, onSubmit });
  const [selected, setSelected] = useState('');

  const steps = [
    {
      label: 'Details',
      content: (
        <>
          <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
            Add your agents template details
          </Typography>
          <Stack spacing={3} sx={{ backgroundColor: 'white', p: 5, borderRadius: '10px' }}>
            <Typography my="-10px">Agent Name</Typography>

            <Field.Text name="name" placeholder="Type your agent name" />
            <Typography my="-10px">Description</Typography>

            <Field.Text name="description" placeholder="Type your agent description" multiline />
            <Typography my="-10px">System Prompt</Typography>
            <Field.Text
              rows={4}
              name="systemPrompt"
              placeholder="Enter system instructions for the agent"
              multiline
            />
            <Typography>Select Agent Type</Typography>
            <Field.RadioGroup row name="type" sx={{ px: 3 }} options={TYPE_OPTIONS} />
            <Typography>Agent Status</Typography>
            <Field.Switch
              sx={{ px: 5 }}
              name="status"
              options={STATUS_OPTIONS}
              labelPlacement="end"
              onBlur={() => console.log('Blur event')}
            />
          </Stack>
        </>
      ),
    },
    {
      label: 'Category',
      content: (
        <Stack spacing={2}>
          <Typography color="rgba(15, 14, 17, 0.65)" variant="h5">
            Select your agents template category
          </Typography>
          <Box bgcolor="white" p="20px" borderRadius="10px">
            <ServiceSearchBar
              query={categorySearchQuery}
              onChange={handleCategorySearchChange}
              placeholder="Search categories..."
            />

            <Field.RadioGroup
              name="category"
              options={options}
              sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr', 
                gap: 1,
                '& .MuiFormControlLabel-root': {
                  bgcolor: 'divider',
                  width: '27.5vw',
                  borderRadius: 1,
                  p: 1,
                  boxShadow: 1,
                  display: 'flex',
                  alignItems: 'center',
                },
                borderRadius: 1,
                p: 2,
              }}
            />
          </Box>
        </Stack>
      ),
    },
    {
      label: 'Tools',
      content: (
        <>
          <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
            Add tools to your agents template
          </Typography>
          <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
            <ServiceSearchBar
              query={toolSearchQuery}
              onChange={handleToolSearchChange}
              placeholder="Search tools..."
            />
            <Grid container spacing={2}>
              {availableTools.map((tool) => {
                const isSelected = selectedTools.includes(tool.id);
                return (
                  <Grid item xs={12} key={tool.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        bgcolor: 'divider',
                      }}
                      onClick={() => handleToolToggle(tool.id)}
                    >
                      <CardContent>
                        <FormControlLabel
                          control={<Checkbox checked={isSelected} />}
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Iconify icon="mdi:tools" />
                              <Typography variant="subtitle2">{tool.name}</Typography>
                            </Stack>
                          }
                          sx={{ width: '100%' }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          </Stack>
        </>
      ),
    },
    {
      label: 'LLM Models',
      content: (
        <>
          {' '}
          <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
            Choose Channels
          </Typography>
          <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
            <ServiceSearchBar
              query={channelSearchQuery}
              onChange={handleChannelSearchChange}
              placeholder="Search channels..."
            />
            <Grid container spacing={2}>
              {CHANNEL_OPTIONS.map((channel) => {
                const isSelected = selectedChannels.includes(channel.value);
                return (
                  <Grid item xs={12} sm={12} key={channel.value}>
                    <Card
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        backgroundColor: 'divider',
                      }}
                      onClick={() => handleChannelToggle(channel.value)}
                    >
                      <CardContent>
                        <FormControlLabel
                          control={<Checkbox checked={isSelected} />}
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Iconify icon={channel.icon} />
                              <Typography variant="subtitle2">{channel.label}</Typography>
                            </Stack>
                          }
                          sx={{ width: '100%' }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          </Stack>
        </>
      ),
    },
    // {
    //   label: 'Summary',
    //   content: (
    //     <Stack spacing={3}>
    //       <Typography variant="h6">Summary</Typography>
    //       <Typography>Review your agents template details before creation.</Typography>
    //     </Stack>
    //   ),
    // },
  ];

  return (
    <>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
        <IconButton onClick={() => window.history.back()}>
          <Iconify icon="mdi:arrow-left" />
        </IconButton>
        <Typography variant="body1">Back</Typography>
      </Stack>
      <AppContainer
        title={agent ? 'Edit Agent Template' : 'Create New Agents Template'}
        routeLinks={[
          { name: 'Agent`s Templates', href: paths.dashboard.agents.root },
          { name: `${agent ? 'Edit Agent Template' : 'Create New Agents Template'}` },
        ]}
        buttons={[
          {
            label: t('components.buttons.createNewTeam'),
            variant: 'outlined',
            startIcon: <Iconify icon="eva:plus-fill" />,
          },
        ]}
      >
        <Form methods={methods} onSubmit={onFormSubmit}>
          <Stepper
            activeStep={activeStep}
            alternativeLabel
            sx={{
              my: 3,
              '& .MuiStepConnector-line': {
                mt: '20px',
              },
            }}
          >
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  sx={{
                    pt: 2,

                    mx: 8.5,
                    borderRadius: 5,
                    ...(activeStep === index && {
                      bgcolor: 'rgba(163, 139, 233, 0.13)',
                      width: '65%',
                      color: 'common.white',
                      '& .MuiStepLabel-label': {
                        color: 'common.black',
                      },
                      '& .MuiStepConnector-line': {
                        borderColor: 'primary.main',
                        borderTopWidth: 2,
                      },
                      '& .MuiStepIcon-root': {
                        color: 'white',
                        border: '2px solid',
                        borderColor: 'primary.main',
                        borderRadius: '50%',
                      },
                    }),
                    ...(index < activeStep && {
                      '& .MuiStepIcon-root': {
                        color: 'success.main',

                        bgcolor: 'common.white',
                        borderRadius: '50%',
                      },
                    }),
                  }}
                >
                  {step.label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          <Container>
            <Box
              sx={{
                p: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'divider',
                borderRadius: 1,
              }}
            >
              {steps[activeStep].content}
            </Box>
          </Container>

          <Stack direction="row" justifyContent="space-between" sx={{ mt: 3 }}>
            <Box sx={{ flexGrow: 1 }} />
            {activeStep < steps.length - 1 ? (
              <Button
                variant="contained"
                sx={{ backgroundColor: 'primary.main', width: '10vw' }}
                onClick={handleNext}
              >
                Next
              </Button>
            ) : (
              <AppButton
                label={agent ? 'Update Template' : 'Create Template'}
                variant="contained"
                onClick={onFormSubmit}
                isLoading={isLoading}
              />
            )}
          </Stack>
        </Form>
      </AppContainer>
    </>
  );
}
