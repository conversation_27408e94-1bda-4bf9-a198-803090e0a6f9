import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { AgentForm } from 'src/sections/agents/form/agent-form';
import { useTemplatesApi } from 'src/services/api/use-templates-api';

// ----------------------------------------------------------------------

export default function CreateAgentPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const templatesApi = useTemplatesApi();

  // Fetch template data when editing
  const { data: agent } = templatesApi.useGetTemplate(Number(id!));

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${
          id ? 'Edit' : 'Create New'
        } Agent's Template`}</title>
      </Helmet>
      <AgentForm agent={agent ?? null} />
    </>
  );
}
