import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import AgentForm from 'src/sections/agents/form/agent-form';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
import { paths } from 'src/routes/paths';
import { AgentFormValues } from 'src/sections/agents/form/use-agent-form';

// ----------------------------------------------------------------------

export default function CreateAgentPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const templatesApi = useTemplatesApi();

  // Fetch template data when editing
  const { data: agent } = templatesApi.useGetTemplate(Number(id!));

  // Create template mutation
  const createTemplateMutation = templatesApi.useCreateTemplate();

  // Update template mutation
  const updateTemplateMutation = templatesApi.useUpdateTemplate(Number(id!));

  const handleSubmit = async (formData: AgentFormValues) => {
    const requestData = {
      name: formData.name,
      description: formData.description,
      type: formData.type,
      status: formData.status,
      categoryId: formData.categoryId,
      toolsId: formData.toolsId,
      systemMessage: 'Default system message',
      model: 'GPT_4O_MINI' as const,
    };

    if (id) {
      // Update existing agent
      await updateTemplateMutation.mutateAsync(requestData);
    } else {
      // Create new agent
      await createTemplateMutation.mutateAsync(requestData);
    }
    // Navigate back to agents list after success
    navigate(paths.dashboard.agents.root);
  };

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${
          id ? 'Edit' : 'Create New'
        } Agent's Template`}</title>
      </Helmet>
      <AgentForm agent={agent ?? null} onSubmit={handleSubmit} />{' '}
    </>
  );
}
