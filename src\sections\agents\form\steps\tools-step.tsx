import { useState, useMemo } from 'react';
import { Stack } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { FormStepWrapper, SelectionGrid, SearchBar } from 'src/components/form-steps';
import { useToolsApi } from 'src/services/api/use-tools-api';
import { AgentFormValues } from '../config/agent-form-config';

// ----------------------------------------------------------------------

interface ToolsStepProps {
  // Add any specific props if needed
}

export function ToolsStep({}: ToolsStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');
  
  // Get tools from API
  const { useGetTools } = useToolsApi();
  const { data: toolsResponse } = useGetTools();
  const tools = toolsResponse?.tools || [];
  
  // Watch current selection
  const selectedTools = watch('toolsId') || [];
  
  // Filter tools based on search
  const filteredTools = useMemo(() => {
    if (!searchQuery) return tools;
    
    return tools.filter(tool =>
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [tools, searchQuery]);
  
  // Transform tools for SelectionGrid
  const toolItems = filteredTools.map(tool => ({
    id: tool.id,
    label: tool.name,
    description: tool.description,
    icon: 'mdi:tools', // Default icon for tools
  }));
  
  // Handle tool selection
  const handleToolToggle = (toolId: string | number) => {
    const numericToolId = typeof toolId === 'string' ? parseInt(toolId, 10) : toolId;
    const currentTools = selectedTools || [];
    
    const newSelection = currentTools.includes(numericToolId)
      ? currentTools.filter(id => id !== numericToolId)
      : [...currentTools, numericToolId];
    
    setValue('toolsId', newSelection, { shouldValidate: true });
  };
  
  return (
    <FormStepWrapper
      title="Select Tools"
      description="Choose the tools that your agent will have access to"
    >
      <Stack spacing={3}>
        {/* Search Bar */}
        <SearchBar
          query={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search tools..."
        />
        
        {/* Tools Grid */}
        <SelectionGrid
          items={toolItems}
          selectedItems={selectedTools}
          onToggle={handleToolToggle}
          multiSelect={true}
          columns={{ xs: 1, sm: 1, md: 2, lg: 2 }}
        />
      </Stack>
    </FormStepWrapper>
  );
}

export default ToolsStep;
