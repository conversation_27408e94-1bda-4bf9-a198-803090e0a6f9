import { useState, useMemo } from 'react';
import { Stack } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { FormStepWrapper, SelectionGrid, SearchBar } from 'src/components/form-steps';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { AgentFormValues } from '../config/agent-form-config';

// ----------------------------------------------------------------------

interface CategoryStepProps {
  // Add any specific props if needed
}

export function CategoryStep(_props: CategoryStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');
  
  // Get categories from API
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesResponse } = useGetCategories();
  const categories = categoriesResponse?.categories || [];
  
  // Watch current selection
  const selectedCategory = watch('category');
  
  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    if (!searchQuery) return categories;
    
    return categories.filter(category =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [categories, searchQuery]);
  
  // Transform categories for SelectionGrid
  const categoryItems = filteredCategories.map(category => ({
    id: category.id.toString(),
    label: category.name,
    description: category.description,
    icon: category.icon,
  }));
  
  // Handle category selection
  const handleCategoryToggle = (categoryId: string | number) => {
    setValue('category', categoryId.toString(), { shouldValidate: true });
  };
  
  return (
    <FormStepWrapper
      title="Select Category"
      description="Choose the category that best describes your agent's purpose"
    >
      <Stack spacing={3}>
        {/* Search Bar */}
        <SearchBar
          query={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search categories..."
        />
        
        {/* Categories Grid */}
        <SelectionGrid
          items={categoryItems}
          selectedItems={selectedCategory ? [selectedCategory] : []}
          onToggle={handleCategoryToggle}
          multiSelect={false}
          columns={{ xs: 1, sm: 2, md: 2, lg: 2 }}
        />
      </Stack>
    </FormStepWrapper>
  );
}

export default CategoryStep;
