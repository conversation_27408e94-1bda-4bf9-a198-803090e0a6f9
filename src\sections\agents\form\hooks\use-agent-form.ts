import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { paths } from 'src/routes/paths';
import {
  agentFormSchema,
  AgentFormValues,
  DEFAULT_FORM_VALUES,
  FORM_STEPS
} from '../config/agent-form-config';
import {
  templateToFormValues,
  formValuesToSubmission,
  getDefaultFormValues,
  getStepCompletionStatus
} from '../utils/form-utils';

// ----------------------------------------------------------------------

interface UseAgentFormProps {
  agent: Template | null;
}

export function useAgentForm({ agent }: UseAgentFormProps) {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  
  // API hooks
  const { useCreateTemplate, useUpdateTemplate } = useTemplatesApi();
  const { mutate: createTemplate, isPending: isCreating } = useCreateTemplate();
  const { mutate: updateTemplate, isPending: isUpdating } = useUpdateTemplate(agent?.id || 0);
  
  const isLoading = isCreating || isUpdating;
  const isEditing = Boolean(agent);
  
  // Form setup
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentFormSchema),
    defaultValues: getDefaultFormValues(),
  });

  const {
    handleSubmit,
    trigger,
    reset,
    watch,
    formState: { isSubmitting, errors },
  } = methods;

  // Watch all form values for step completion checking
  const formValues = watch();

  // Reset form when agent changes
  useEffect(() => {
    if (agent) {
      // Use utility function to transform template to form values
      const formValues = templateToFormValues(agent);
      reset(formValues);
    } else {
      // Reset to default values for creating new agent
      reset(getDefaultFormValues());
    }
  }, [agent, reset]);
  
  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return;
    
    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as any);
    
    if (isStepValid) {
      if (activeStep < FORM_STEPS.length - 1) {
        setActiveStep(prev => prev + 1);
      }
    }
  }, [activeStep, trigger]);
  
  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);
    }
  }, [activeStep]);
  
  const handleStepClick = useCallback(async (stepIndex: number) => {
    // Validate all previous steps before allowing navigation
    let canNavigate = true;

    for (let i = 0; i < stepIndex; i += 1) {
      const step = FORM_STEPS[i];
      // eslint-disable-next-line no-await-in-loop
      const isStepValid = await trigger(step.fields as any);
      if (!isStepValid) {
        canNavigate = false;
        break;
      }
    }
    
    if (canNavigate) {
      setActiveStep(stepIndex);
    }
  }, [trigger]);
  
  // Form submission
  const onFormSubmit = handleSubmit(async (data: AgentFormValues) => {
    try {
      // Use utility function to transform form values to submission format
      const submissionData = formValuesToSubmission(data);

      if (isEditing) {
        // Update existing template
        await new Promise((resolve, reject) => {
          updateTemplate(submissionData, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      } else {
        // Create new template
        await new Promise((resolve, reject) => {
          createTemplate(submissionData, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      }

      // Navigate back to agents list
      navigate(paths.dashboard.agents.root);
    } catch (error) {
      console.error('Form submission error:', error);
      // Error handling could be improved with toast notifications
    }
  });
  
  // Helper functions
  const getCurrentStepErrors = useCallback(() => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return [];
    
    return currentStep.fields.filter(field => errors[field]);
  }, [activeStep, errors]);
  
  const isStepCompleted = useCallback((stepIndex: number) => {
    const step = FORM_STEPS[stepIndex];
    if (!step) return false;

    const { isCompleted } = getStepCompletionStatus(step.fields, errors, formValues);
    return isCompleted;
  }, [errors, formValues]);
  
  const canProceedToNext = useCallback(() => {
    return getCurrentStepErrors().length === 0;
  }, [getCurrentStepErrors]);
  
  return {
    // Form state
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    
    // Navigation
    handleNext,
    handleBack,
    handleStepClick,
    
    // Form submission
    onFormSubmit,
    
    // Helper functions
    getCurrentStepErrors,
    isStepCompleted,
    canProceedToNext,
    
    // Constants
    steps: FORM_STEPS,
    totalSteps: FORM_STEPS.length,
    isLastStep: activeStep === FORM_STEPS.length - 1,
    isFirstStep: activeStep === 0,
  };
}

export default useAgentForm;
