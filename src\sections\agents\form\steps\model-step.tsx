import { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON>po<PERSON>, Grid, Card, CardContent, Checkbox, FormControlLabel } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { LLM_MODEL_OPTIONS, AgentFormValues } from '../config/agent-form-config';
import ServiceSearchBar from '../components/service-search-bar';

// ----------------------------------------------------------------------

interface ModelStepProps {
  // Add any specific props if needed
}

export function ModelStep(_props: ModelStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Watch current selection
  const selectedChannels = watch('model') ? [watch('model')] : [];

  // Filter channels based on search
  const filteredChannels = useMemo(() => {
    if (!searchQuery) return LLM_MODEL_OPTIONS;

    return LLM_MODEL_OPTIONS.filter(model =>
      model.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.provider.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  // Handle model selection
  const handleChannelToggle = (modelValue: string) => {
    setValue('model', modelValue as AgentFormValues['model'], { shouldValidate: true });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Choose Channels
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search channels..."
        />
        <Grid container spacing={2}>
          {filteredChannels.map((channel) => {
            const isSelected = selectedChannels.includes(channel.value);
            return (
              <Grid item xs={12} sm={12} key={channel.value}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: 'divider',
                  }}
                  onClick={() => handleChannelToggle(channel.value)}
                >
                  <CardContent>
                    <FormControlLabel
                      control={<Checkbox checked={isSelected} />}
                      label={
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify icon={channel.icon} />
                          <Typography variant="subtitle2">{channel.label}</Typography>
                        </Stack>
                      }
                      sx={{ width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Stack>
    </>
  );
}

export default ModelStep;
