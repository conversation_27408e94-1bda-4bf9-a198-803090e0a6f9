import { useState, useMemo } from 'react';
import { Stack, Typography, Chip, Box } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { FormStepWrapper, SelectionGrid, SearchBar, SelectionItem } from 'src/components/form-steps';
import { LLM_MODEL_OPTIONS, AgentFormValues } from '../config/agent-form-config';

// ----------------------------------------------------------------------

interface ModelStepProps {
  // Add any specific props if needed
}

export function ModelStep(_props: ModelStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');
  
  // Watch current selection
  const selectedModel = watch('model');
  
  // Filter models based on search
  const filteredModels = useMemo(() => {
    if (!searchQuery) return LLM_MODEL_OPTIONS;
    
    return LLM_MODEL_OPTIONS.filter(model =>
      model.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.provider.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);
  
  // Transform models for SelectionGrid
  const modelItems = filteredModels.map(model => ({
    id: model.value,
    label: model.label,
    description: model.description,
    icon: model.icon,
  }));
  
  // Handle model selection
  const handleModelToggle = (modelId: string | number) => {
    setValue('model', modelId as AgentFormValues['model'], { shouldValidate: true });
  };
  
  // Custom content renderer for models
  const renderModelContent = (item: SelectionItem) => {
    const modelOption = LLM_MODEL_OPTIONS.find(m => m.value === item.id);
    const isSelected = selectedModel === item.id;
    
    return (
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" spacing={1.5}>
            <Box
              sx={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                border: '2px solid',
                borderColor: isSelected ? 'primary.main' : 'divider',
                bgcolor: isSelected ? 'primary.main' : 'transparent',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {isSelected && (
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'common.white',
                  }}
                />
              )}
            </Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              {item.label}
            </Typography>
          </Stack>
          
          {modelOption && (
            <Chip
              label={modelOption.provider}
              size="small"
              variant="soft"
              color="primary"
            />
          )}
        </Stack>
        
        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
          {item.description}
        </Typography>
      </Stack>
    );
  };
  
  return (
    <FormStepWrapper
      title="Select AI Model"
      description="Choose the AI model that will power your agent"
    >
      <Stack spacing={3}>
        {/* Search Bar */}
        <SearchBar
          query={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search AI models..."
        />
        
        {/* Models Grid */}
        <SelectionGrid
          items={modelItems}
          selectedItems={selectedModel ? [selectedModel] : []}
          onToggle={handleModelToggle}
          multiSelect={false}
          columns={{ xs: 1, sm: 1, md: 1, lg: 2 }}
          renderCustomContent={renderModelContent}
        />
      </Stack>
    </FormStepWrapper>
  );
}

export default ModelStep;
